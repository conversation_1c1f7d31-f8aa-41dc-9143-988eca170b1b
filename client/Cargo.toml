[package]
name = "client"
version = "0.1.0"
edition = "2021"

[dependencies]
mpl-token-metadata = { version = "5.1.0" }
anchor-client = "0.31.1"
anchor-lang = "0.31.1"
raydium-amm-v3 = { path = "../programs/amm", features = [
    "no-entrypoint",
    "client",
] }
solana-sdk = "=2.1.0"
solana-client = "=2.1.0"
solana-account-decoder = "=2.1.0"
solana-transaction-status = "=2.1.0"
spl-token = { version = "7.0.0", features = ["no-entrypoint"] }
spl-token-client = "0.14.0"
spl-memo = "6.0.0"
spl-associated-token-account = { version = "6.0.0", features = [
    "no-entrypoint",
] }
spl-token-2022 = { version = "7.0.0", features = ["no-entrypoint"] }
clap = { version = "4.1.8", features = ["derive"] }
anyhow = "1.0.32"
rand = "0.9.0"
hex = "0.4.3"
configparser = "3.0.0"
serde_json = { version = "1.0.78" }
serde = { version = "1.0", features = ["derive"] }
arrayref = "0.3.7"
bs58 = { version = "0.5.0" }
bincode = { version = "1.3.3" }
regex = "1"
colorful = "0.3.2"
base64 = "0.21.0"
