[Global]
http_url = https://api.devnet.solana.com
ws_url = wss://api.devnet.solana.com/
payer_path = id.json
admin_path = adMCyoCgfkg7bQiJ9aBJ59H3BXLY3r5LNLfPpQfMzBe.json
raydium_v3_program = devi51mZmdwUJGU9hjN27vEz64Gps7uUefqxg27EAtH
slippage = 0.01

[Pool]
mint0 = 2SiSpNowr7zUv5ZJHuzHszskQNaskWsNukhivCtuVLHo
mint1 = GfmdKWR1KrttDsQkJfwtXovZw9bUBHYkPAEwB6wZqQvJ
# fee: 0.05%  ==》 tick_spacing: 10
# fee: 0.30%  ==》 tick_spacing: 60
# fee: 1.00%  ==》 tick_spacing: 200

# fee: 1 / 10000    ==》 tick_spacing: 10
# fee: 25 / 10000    ==》 tick_spacing: 60
# protocol_fee: 12 / 100
amm_config_index = 1