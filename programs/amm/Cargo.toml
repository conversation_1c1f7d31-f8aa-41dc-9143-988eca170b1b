[package]
name = "raydium-amm-v3"
version = "0.1.0"
description = "Anchor client and source for Raydium concentrated liquidity AMM"
edition = "2021"
keywords = ["solana", "anchor", "raydium"]

[lib]
crate-type = ["cdylib", "lib"]
name = "raydium_amm_v3"
doctest = false

[features]
no-entrypoint = []
cpi = ["no-entrypoint"]
default = []
client = []
no-log-ix-name = []
enable-log = []
devnet = []
paramset = []
idl-build = ["anchor-lang/idl-build", "anchor-spl/idl-build"]

[dependencies]
anchor-lang = { version = "0.31.1", features = ["init-if-needed"] }
anchor-spl = { version = "0.31.1", features = ["metadata", "memo"] }
spl-token-2022 = { version = "7.0.0", features = ["no-entrypoint"] }
uint = { git = "https://github.com/raydium-io/parity-common", package = "uint" }
bytemuck = { version = "1.19.0", features = ["derive", "min_const_generics"] }
arrayref = { version = "0.3.6" }
solana-security-txt = "1.1.1"

[dev-dependencies]
quickcheck = "0.9"
proptest = "1.0"
rand = "0.8.5"

[profile.release]
lto = "fat"
codegen-units = 1
panic = "abort"
overflow-checks = true
[profile.release.build-override]
opt-level = 3
incremental = false
codegen-units = 1
